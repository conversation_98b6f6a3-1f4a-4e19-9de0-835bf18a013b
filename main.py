import json
import re
import time
import asyncio
from telethon import TelegramClient, events

BOT_TOKENS=['**********************************************']

API_ID = '********'
API_HASH = 'cc947cf347be6016a0c304c4aa75af5e'
client = TelegramClient('testing', API_ID, API_HASH)
# Group mappings with topic support
GROUP_MAPPINGS = {
    -**********: {  # Source group
        'destination': -**********,  # Default destination group
        'topics': {  # Topic-specific routing
            1: { # Topic ID
                'destination': -**********, # Special destination for this topic
                'forward_all': True # Forward all messages without keyword filtering

            },
            2: { # Topic ID
                'destination': -********** # Use default destination
            }
        }
    },
    -**********: {  # Source group (no topics, track all)
        'destination': -**********   # Destination group
    }
}

# Load keyword lists from external JSON file (keywords.json)
keywords_file = "keywords.json"
try:
    with open(keywords_file, "r", encoding="utf-8") as f:
        keywords_data = json.load(f)
        POSITIVE_KEYWORDS = keywords_data.get("positive", [])
        NEGATIVE_KEYWORDS = keywords_data.get("negative", [])
        MAYBE_KEYWORDS = keywords_data.get("maybe", [])
except (FileNotFoundError, json.JSONDecodeError):
    POSITIVE_KEYWORDS = []
    NEGATIVE_KEYWORDS = []
    MAYBE_KEYWORDS = []

bot_clients = []
for i, token in enumerate(BOT_TOKENS):
    bot = TelegramClient(f'bot_session_{i}', API_ID, API_HASH)
    bot.start(bot_token=token)
    bot_clients.append(bot)

bot_index = 0  # Global index for round-robin bot selection

# Maintain a context (a simple sliding window per chat)
chat_context = {}
TIME_THRESHOLD = 300  # seconds (5 minutes)

def extract_token(text):
    pattern = r'\b[1-9A-HJ-NP-Za-km-z]{32,44}pump\b'
    tokens = re.findall(pattern, text)
    return tokens[0] if tokens else None

async def send_message_to_group(original_chat_id, message_text, topic_id=None):
    """
    Sends the message to the corresponding group based on the mapping
    using one of the bot clients in a round-robin manner.
    """
    group_config = GROUP_MAPPINGS.get(original_chat_id)
    if group_config is None:
        print(f"⚠️ No destination group found for chat ID {original_chat_id}")
        return

    destination_id = group_config.get('destination') # Use .get for safety

    # Check for topic-specific destination
    if isinstance(group_config.get('topics'), dict) and topic_id in group_config['topics']:
        topic_config = group_config['topics'][topic_id]
        if 'destination' in topic_config:
            destination_id = topic_config['destination']

    if destination_id is None:
        print(f"⚠️ No destination configured for chat ID {original_chat_id} and topic ID {topic_id}")
        return

    global bot_index
    num_bots = len(bot_clients)
    attempts = 0
    sent = False
    while attempts < num_bots and not sent:
        current_bot = bot_clients[bot_index]
        current_index = bot_index
        bot_index = (bot_index + 1) % num_bots
        try:
            await current_bot.send_message(
                entity=destination_id,
                message=message_text
            )
            print(f"📨 Sent message to group {destination_id} using bot index {current_index}")
            sent = True
        except Exception as e:
            print(f"❌ Bot at index {current_index} failed to send message: {e}")
            attempts += 1
            await asyncio.sleep(1)
    if not sent:
        print("❌ All bots failed to send message.")

@client.on(events.NewMessage)
async def handler(event):
    print(event)
    chat_id = event.chat_id
    topic_id = None # Initialize topic_id

    # Only process messages from the groups we're tracking
    if chat_id not in GROUP_MAPPINGS:
        return

    # Check if this is a topic message and get the topic ID
    # In forum groups, messages in topics have reply_to set
    if hasattr(event, 'reply_to') and event.reply_to:
        print(f"🔍 Debug: reply_to found - reply_to_top_id: {getattr(event.reply_to, 'reply_to_top_id', None)}, reply_to_msg_id: {getattr(event.reply_to, 'reply_to_msg_id', None)}")
        # If it's a reply to another message in a topic, reply_to_top_id contains the topic ID
        if hasattr(event.reply_to, 'reply_to_top_id') and event.reply_to.reply_to_top_id:
            topic_id = event.reply_to.reply_to_top_id
            print(f"🎯 Topic ID detected from reply_to_top_id: {topic_id}")
        # If it's a direct message in a topic (not a reply), reply_to_msg_id contains the topic ID
        elif hasattr(event.reply_to, 'reply_to_msg_id') and event.reply_to.reply_to_msg_id:
            topic_id = event.reply_to.reply_to_msg_id
            print(f"🎯 Topic ID detected from reply_to_msg_id: {topic_id}")
    else:
        print(f"🔍 Debug: No reply_to found - this might be a general chat message")

    group_config = GROUP_MAPPINGS[chat_id]
    print(f"📋 Group config for {chat_id}: {group_config}")

    # Handle topic-based filtering and routing
    if 'topics' in group_config:
        print(f"🎯 Group has topic configuration. Detected topic_id: {topic_id}")
        if topic_id is None:
            # Not a topic message, but this group is topic-filtered, so skip.
            print(f"⚠️ Skipping message: Group is topic-filtered but message has no topic_id")
            return

        if isinstance(group_config['topics'], dict):
            # New dictionary-based topic config
            print(f"📝 Checking if topic {topic_id} is in tracked topics: {list(group_config['topics'].keys())}")
            if topic_id not in group_config['topics']:
                print(f"⚠️ Skipping message: Topic {topic_id} not in tracked topics")
                return # Topic not tracked

            topic_config = group_config['topics'][topic_id]
            print(f"🎯 Topic config for {topic_id}: {topic_config}")
            if topic_config.get('forward_all', False):
                # Forward all messages for this topic without keyword filtering
                print(f"✅ Forwarding all messages for topic {topic_id} without keyword filtering")
                await send_message_to_group(chat_id, event.raw_text, topic_id=topic_id)
                return

        elif isinstance(group_config['topics'], list):
            # Old list-based topic config
            print(f"📝 Checking if topic {topic_id} is in tracked topic list: {group_config['topics']}")
            if topic_id not in group_config['topics']:
                print(f"⚠️ Skipping message: Topic {topic_id} not in tracked topic list")
                return # Topic not in the allowed list
    else:
        print(f"📋 Group has no topic configuration - processing all messages")

    message_text = event.raw_text
    current_time = time.time()
    timestamp = event.date.strftime('%Y-%m-%d %H:%M:%S')

    # Get chat name (fallback to "Chat <id>" if unavailable)
    chat_title = getattr(event.chat, "title", f"Chat {chat_id}")

    # Get sender info
    sender = await event.get_sender()
    sender_username = sender.username if sender and sender.username else f"User {sender.id}"
    
    # Get topic info if available (topic_id was already determined above)
    topic_info = ""
    if topic_id:
        topic_info = f" (Topic ID: {topic_id})"
    
    print(f"📩 New message received from chat {chat_id}{topic_info}: {message_text}")

    # Initialize chat context if not present
    if chat_id not in chat_context:
        chat_context[chat_id] = []

    # Look for token mentions in the message
    token = extract_token(message_text)
    if token:
        chat_context[chat_id].append({'token': token, 'time': current_time})
        print(f"🔍 Detected token: {token}")
        await send_message_to_group(chat_id, f"🔍 Detected token: {token} Sent by: @{sender_username}{topic_info}")

    # Check for keyword matches (positive, negative, and maybe)
    positive_matches = [pattern for pattern in POSITIVE_KEYWORDS if re.search(pattern, message_text, re.IGNORECASE)]
    negative_matches = [pattern for pattern in NEGATIVE_KEYWORDS if re.search(pattern, message_text, re.IGNORECASE)]
    maybe_matches = [pattern for pattern in MAYBE_KEYWORDS if re.search(pattern, message_text, re.IGNORECASE)]

    positive_found = bool(positive_matches)
    negative_found = bool(negative_matches)
    maybe_found = bool(maybe_matches)

    # If any signal keyword is detected, associate it with the most recent token (if any)
    if positive_found or negative_found or maybe_found:
        recent_tokens = chat_context[chat_id]
        if recent_tokens:
            associated_token = recent_tokens[-1]['token']
            if positive_found:
                sentiment = f"{message_text.strip()} ✅ Bullish"
            elif negative_found:
                sentiment = f"{message_text.strip()} ❌ Bearish"
            elif maybe_found:
                sentiment = f"{message_text.strip()} 🟠 Maybe"
            signal_message = (
                f"{sentiment.replace('\\b', '*')} signal detected for {associated_token} "
                f"📍 From: {chat_title} ({chat_id}){topic_info}\n"
                f"👤 User: {sender_username}\n"
                f"⏰ Time: {timestamp}"
            )
            print(f"📢 Sending signal: {signal_message}")
            await send_message_to_group(chat_id, signal_message, topic_id=topic_id)
        else:
            print("⚠️ No recent token found, skipping signal.")

    # Clean up old context entries beyond the time threshold
    chat_context[chat_id] = [entry for entry in chat_context[chat_id] if current_time - entry['time'] <= TIME_THRESHOLD]

def run_bot():
    print("🚀 Bot is running...")
    with client:
        client.run_until_disconnected()

if __name__ == '__main__':
    run_bot()