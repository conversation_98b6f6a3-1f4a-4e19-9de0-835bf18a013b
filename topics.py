from telethon import TelegramClient
from telethon.tl.functions.channels import GetForumTopicsRequest

API_ID = 12728795
API_HASH = "cc947cf347be6016a0c304c4aa75af5e"

GROUP_ID = -1002951881464

client = TelegramClient("testing", API_ID, API_HASH)

async def get_topics():
    result = await client(GetForumTopicsRequest(
        channel=GROUP_ID,
        offset_date=None,
        offset_id=0,
        offset_topic=0,
        limit=100
    ))

    for topic in result.topics:
        print(f"📝 Topic: {topic.title} | ID: {topic.id}")

with client:
    client.loop.run_until_complete(get_topics())
